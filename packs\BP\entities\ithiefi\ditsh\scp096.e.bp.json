{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:scp096", "is_spawnable": true, "is_summonable": true, "properties": {"ditsh:playing_music": {"type": "bool", "client_sync": false, "default": false}, "ditsh:looked_at": {"type": "bool", "client_sync": true, "default": false}}}, "component_groups": {"ditsh:music_playing": {"minecraft:timer": {"looping": true, "time": 14, "time_down_event": {"event": "ditsh:check_loop"}}}, "ditsh:sitting": {"minecraft:looked_at": {"search_radius": 64, "set_target": "never", "find_players_only": true, "field_of_view": 360, "min_looked_at_duration": 1, "scale_fov_by_distance": false, "line_of_sight_obstruction_type": "collision_for_camera", "filters": {"test": "bool_property", "subject": "self", "domain": "ditsh:looked_at", "value": false}, "looked_at_event": {"event": "ditsh:looked_at", "target": "self"}}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no"}]}, "minecraft:is_collidable": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}}, "ditsh:raging": {"minecraft:timer": {"time": 30, "looping": false, "time_down_event": {"event": "ditsh:start_attacking", "target": "self"}}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no"}]}, "minecraft:is_collidable": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}}, "ditsh:attack_target": {"minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true, "horizontal_reach": 0.8, "cooldown_time": 1, "can_spread_on_fire": true, "speed_multiplier": 1.8, "on_kill": {"event": "ditsh:on_kill", "target": "self"}}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 0, "target_search_height": -1, "reselect_targets": true, "cooldown": 0, "attack_interval": 0, "must_reach": false, "must_see": false, "reevaluate_description": true, "entity_types": [{"priority": 0, "filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "max_dist": 256}]}, "minecraft:angry": {"angry_sound": "angry", "duration": 25, "duration_delta": 0, "sound_interval": [2, 4], "calm_event": {"event": "ditsh:on_calm", "target": "self"}}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ditsh:sitting"]}}, "ditsh:on_death": {}, "ditsh:on_kill": {"remove": {"component_groups": ["ditsh:attack_target", "ditsh:raging"]}, "queue_command": {"command": "playsound mob.ditsh.scp096.kill @a ~ ~ ~"}}, "ditsh:start_chase_music": {"add": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": true}}, "ditsh:stop_chase_music": {"remove": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": false}}, "ditsh:maintain_chase_music": {}, "ditsh:check_loop": {"first_valid": [{"filters": {"test": "has_target", "subject": "self", "value": true}, "trigger": {"event": "ditsh:maintain_chase_music"}}, {"filters": {"test": "has_target", "subject": "self", "value": false}, "trigger": {"event": "ditsh:stop_chase_music"}}]}, "ditsh:looked_at": {"add": {"component_groups": ["ditsh:raging"]}, "remove": {"component_groups": ["ditsh:sitting"]}, "set_property": {"ditsh:looked_at": true}}, "ditsh:start_attacking": {"add": {"component_groups": ["ditsh:attack_target"]}, "remove": {"component_groups": ["ditsh:raging"]}}, "ditsh:start_sitting": {"add": {"component_groups": ["ditsh:sitting"]}, "set_property": {"ditsh:looked_at": false}}, "ditsh:on_calm": {"remove": {"component_groups": ["ditsh:attack_target"]}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 12 + (query.equipment_count * Math.Random(8,24)) : 0"}, "minecraft:type_family": {"family": ["ditsh", "scp096", "mob", "monster"]}, "minecraft:collision_box": {"width": 0.5, "height": 2.4}, "minecraft:health": {"value": 60, "max": 60}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:movement": {"value": 0.2}, "minecraft:movement.basic": {}, "minecraft:navigation.walk": {"can_open_doors": true, "can_open_iron_doors": true, "avoid_damage_blocks": true, "avoid_water": false, "can_path_over_water": true, "can_path_over_lava": true, "can_sink": false, "is_amphibious": true}, "minecraft:behavior.float": {"priority": 0}, "minecraft:attack": {"damage": 3.5}, "minecraft:follow_range": {"value": 1024, "max": 1024}, "minecraft:jump.static": {}, "minecraft:pushable": {}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:on_death": {"event": "ditsh:on_death", "target": "self"}, "minecraft:environment_sensor": {"triggers": [{"event": "ditsh:start_chase_music", "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": true}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": false}]}}]}}}}